"""
模型配置管理器
支持动态覆盖agent的模型配置，同时禁止修改coordinator_bot的配置
"""

import tempfile
import os
import yaml
from typing import Dict, Any, Optional
from src.utils.logger import logger


class ModelConfigManager:
    """模型配置管理器，支持动态模型配置覆盖"""
    
    def __init__(self):
        self.original_configs = {}  # 存储原始配置
        self.temp_config_files = []  # 存储临时配置文件路径
        self._original_load_config = None  # 存储原始的_load_config函数
    
    def override_agent_model(self, agent_name: str, model_provider: str, model_name: str) -> str:
        """
        为指定agent创建临时配置文件，覆盖模型设置
        
        Args:
            agent_name: agent名称
            model_provider: 模型提供者 (xm, openrouter)
            model_name: 模型名称
            
        Returns:
            str: 临时配置文件路径
        """
        config_file = f"{agent_name}.yml"
        
        try:
            # 加载原始配置
            from src.utils.resource_manager import load_resource
            yaml_content = load_resource("data_fetcher_bot_config", config_file)
            
            if not yaml_content:
                logger.warning(f"无法加载配置文件: {config_file}")
                return config_file
            
            # 解析YAML配置
            config = yaml.safe_load(yaml_content)
            
            # 保存原始配置
            if agent_name not in self.original_configs:
                self.original_configs[agent_name] = {
                    'model_provider': config.get('model_provider'),
                    'model': config.get('model')
                }
            
            # 覆盖模型配置
            config['model_provider'] = model_provider
            config['model'] = model_name
            
            # 创建临时配置文件
            temp_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.yml', 
                prefix=f'{agent_name}_temp_',
                delete=False,
                encoding='utf-8'
            )
            
            yaml.dump(config, temp_file, default_flow_style=False, allow_unicode=True)
            temp_file.close()
            
            self.temp_config_files.append(temp_file.name)
            
            logger.info(f"为 {agent_name} 创建临时配置: {model_provider}/{model_name}")
            logger.debug(f"临时配置文件: {temp_file.name}")
            
            return temp_file.name
            
        except Exception as e:
            logger.error(f"创建临时配置失败 {agent_name}: {e}")
            return config_file
    
    def apply_model_overrides(self, model_overrides: Dict[str, Dict[str, str]]):
        """
        批量应用模型覆盖配置
        
        Args:
            model_overrides: 格式为 {agent_name: {provider: str, model: str}}
        """
        if not model_overrides:
            return
            
        logger.info(f"应用模型覆盖配置: {model_overrides}")
        
        # 动态修补配置加载函数
        self._patch_config_loader(model_overrides)
    
    def _patch_config_loader(self, model_overrides: Dict[str, Dict[str, str]]):
        """动态修补配置加载函数以支持模型覆盖"""
        try:
            from src.services.agent.bots import data_fetcher_bot
            
            # 保存原始的_load_config函数（如果还没有保存的话）
            if self._original_load_config is None:
                self._original_load_config = data_fetcher_bot._load_config
            
            def patched_load_config(config_file: str) -> Dict[str, Any]:
                # 调用原始函数获取配置
                config = self._original_load_config(config_file)
                
                # 提取agent名称（去掉.yml后缀）
                agent_name = config_file.replace('.yml', '')
                
                # 禁止覆盖coordinator_bot的模型配置
                if agent_name == 'coordinator_bot':
                    logger.debug(f"跳过模型覆盖 {agent_name} (不允许覆盖coordinator_bot配置)")
                    return config
                
                # 如果有覆盖配置，则应用（跳过coordinator_bot）
                if agent_name in model_overrides and agent_name != 'coordinator_bot':
                    override = model_overrides[agent_name]
                    original_provider = config.get('model_provider')
                    original_model = config.get('model')
                    
                    config['model_provider'] = override.get('provider', original_provider)
                    config['model'] = override.get('model', original_model)
                    
                    logger.info(f"应用模型覆盖 {agent_name}: {original_provider}/{original_model} -> {config['model_provider']}/{config['model']}")
                
                return config
            
            # 替换函数
            data_fetcher_bot._load_config = patched_load_config
            logger.debug("成功修补配置加载函数")
            
        except ImportError as e:
            logger.warning(f"无法导入data_fetcher_bot模块，跳过配置修补: {e}")
        except Exception as e:
            logger.error(f"修补配置加载函数时出错: {e}")
    
    def cleanup(self):
        """清理临时文件和恢复原始配置"""
        # 清理临时文件
        for temp_file in self.temp_config_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    logger.debug(f"清理临时配置文件: {temp_file}")
            except Exception as e:
                logger.warning(f"清理临时文件失败 {temp_file}: {e}")
        
        self.temp_config_files.clear()
        
        # 恢复原始的_load_config函数
        if self._original_load_config is not None:
            try:
                from src.services.agent.bots import data_fetcher_bot
                data_fetcher_bot._load_config = self._original_load_config
                logger.debug("成功恢复原始配置加载函数")
            except ImportError as e:
                logger.warning(f"无法导入data_fetcher_bot模块，跳过函数恢复: {e}")
            except Exception as e:
                logger.warning(f"恢复原始配置加载函数时出错: {e}")
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup()


def parse_model_overrides(model_override_str: str) -> Dict[str, Dict[str, str]]:
    """
    解析模型覆盖配置字符串
    
    格式支持:
    1. agent1:provider/model,agent2:provider/model
    2. provider/model (应用到所有agent)
    
    Args:
        model_override_str: 模型覆盖配置字符串
        
    Returns:
        Dict[str, Dict[str, str]]: 解析后的配置
    """
    if not model_override_str:
        return {}
    
    overrides = {}
    
    # 检查是否是全局配置格式 (provider/model)
    if ',' not in model_override_str and ':' not in model_override_str:
        if '/' in model_override_str:
            provider, model = model_override_str.split('/', 1)
            # 应用到所有已知的agent，排除coordinator_bot
            known_agents = [
                "sales_order_analytics", "sales_kpi_analytics", 
                "warehouse_and_fulfillment", "general_chat_bot"
            ]
            for agent in known_agents:
                overrides[agent] = {"provider": provider.strip(), "model": model.strip()}
            logger.info(f"跳过coordinator_bot的模型覆盖（不允许覆盖coordinator_bot配置）")
            return overrides
    
    # 解析具体的agent配置
    for item in model_override_str.split(','):
        item = item.strip()
        if ':' in item:
            agent_name, model_spec = item.split(':', 1)
            agent_name = agent_name.strip()
            model_spec = model_spec.strip()
            
            # 不允许覆盖coordinator_bot的模型配置
            if agent_name == 'coordinator_bot':
                logger.warning(f"不允许覆盖coordinator_bot的模型配置，已忽略: {item}")
                continue
            
            if '/' in model_spec:
                provider, model = model_spec.split('/', 1)
                overrides[agent_name] = {
                    "provider": provider.strip(),
                    "model": model.strip()
                }
            else:
                # 只指定了模型名，使用默认provider
                overrides[agent_name] = {
                    "provider": "openrouter",
                    "model": model_spec
                }
    
    return overrides
