"""
API查询处理器
继承BaseQueryProcessor，为API接口提供统一的查询处理功能
"""
import json
import queue
import os
import time

from typing import Generator

from src.services.agent.base_query_processor import (
    BaseQueryProcessor, QueryRequest, THREAD_POOL
)
from src.utils.logger import logger

# 心跳间隔（秒），用于在AI长时间无输出时保持HTTP连接存活
STREAM_HEARTBEAT_INTERVAL = int(os.getenv("STREAM_HEARTBEAT_INTERVAL", 10))


class APIQueryProcessor(BaseQueryProcessor):
    """API查询处理器 - 继承BaseQueryProcessor复用核心逻辑"""

    def __init__(self):
        super().__init__(THREAD_POOL)

    async def process_stream_events(self, result, message_queue: queue.Queue, streaming_message_id: int = None) -> tuple:
        """API流事件处理逻辑 - 复用基类的统一处理"""
        # 直接使用基类的统一流事件处理逻辑
        return await super().process_stream_events(result, message_queue, streaming_message_id)

    def handle_message_output(self, message: dict) -> str:
        """实现API特有的消息输出格式"""
        return message.get("content", "")

    def run_query(self, user_query: str, user_info: dict = {}, access_token: str = None,
                  conversation_id: str = None, images: list = None,
                  agent_model_overrides: dict = None, agent: str = None) -> Generator:
        """
        API查询处理的主入口函数

        Args:
            user_query: 用户查询
            user_info: 用户信息字典
            access_token: 访问令牌
            conversation_id: 对话ID
            images: 图片列表
            agent_model_overrides: agent模型覆盖配置，格式为 {agent_name: {provider: str, model: str}}
            agent: 指定要使用的特定agent名称（可选）

        Returns:
            Generator: 流式响应生成器
        """
        # 创建查询请求对象
        request = QueryRequest(
            user_query=user_query,
            user_info=user_info,
            access_token=access_token,
            conversation_id=conversation_id,
            images=images,
            agent_model_overrides=agent_model_overrides,
            agent=agent
        )

        # 创建消息队列
        message_queue = queue.Queue()

        # 创建异步工作函数
        async_worker = self.create_async_worker(request, message_queue)

        # 在线程池中执行异步工作
        self.thread_pool.submit(async_worker)

        # 返回流响应生成器
        return self._generate_api_response(request, message_queue)

    def _generate_api_response(self, request: QueryRequest, message_queue: queue.Queue) -> Generator:
        """生成API流式响应

        设计说明（中文）：
        - 为了解决反向代理/浏览器在长时间无数据时断开HTTP长连接的问题，这里实现了心跳保活。
        - 机制：以 STREAM_HEARTBEAT_INTERVAL 为周期，若队列中暂无新消息，则发送最小化的心跳行。
        - 心跳消息遵循当前前端解析协议（以"[data]:"为前缀的单行JSON），type=heartbeat且不带content，避免污染日志。
        - 这样即使AI长时间思考没有输出，也能持续有字节流出，连接不会被中间层闲置超时关闭。
        """
        interrupted = False

        try:
            while True:
                try:
                    # 短超时轮询队列，配合心跳保活
                    message = message_queue.get(timeout=STREAM_HEARTBEAT_INTERVAL)
                except queue.Empty:
                    # 周期性发送心跳，避免连接因空闲被断开
                    try:
                        yield "[data]:" + json.dumps({"type": "heartbeat", "ts": int(time.time())}, ensure_ascii=False) + "\n"
                    except Exception as e:
                        logger.debug(f"发送心跳失败: {e}")
                    continue

                if message is None:
                    # 流式更新架构下，最终结果已在基类中处理完成
                    logger.debug("API流式处理完成")
                    break

                if isinstance(message, dict):
                    # 过滤掉不能序列化的消息类型
                    msg_type = message.get("type")
                    if msg_type in ["bot_instance", "final_result", "used_agents", "time_spend"]:
                        # 这些消息类型包含不能序列化的对象，跳过
                        continue

                    # 转发可序列化的消息到客户端
                    try:
                        yield "[data]:" + json.dumps(message, ensure_ascii=False) + "\n"
                    except (TypeError, ValueError) as e:
                        logger.warning(f"消息序列化失败，跳过: {e}")
                        continue

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            interrupted = True
            logger.warning(f"API客户端断开连接: {e}")
        except Exception as e:
            logger.exception(f"API生成器错误: {e}")
        finally:
            if interrupted:
                # 处理客户端断开连接
                self.handle_client_disconnect(request)




# 创建全局API查询处理器实例
api_processor = APIQueryProcessor()


# 优雅关闭函数
def shutdown_gracefully():
    """优雅关闭所有资源"""
    logger.info("开始优雅关闭API查询处理器...")

    # 关闭BaseQueryProcessor资源
    BaseQueryProcessor.shutdown_gracefully()

    logger.info("API查询处理器优雅关闭完成")


# 在应用退出时调用
import atexit
atexit.register(shutdown_gracefully)


# 注册优雅关闭函数
import atexit
atexit.register(shutdown_gracefully)
