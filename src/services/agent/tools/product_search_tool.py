import hashlib
import logging
import requests
from datetime import datetime
from typing import List, Dict, Any, Union
from src.services.agent.tools.tool_manager import tool_manager
from src.services.xianmudb.query_service import execute_business_query
from src.utils.logger import logger
from src.models.query_result import SQLQueryResult

token_cache = {}
category_cache = {}
category_type_cache = {}


def init_category_cache():
    """初始化类目缓存"""
    if category_cache:  # 如果已经初始化过，直接返回
        return

    try:
        # 初始化的过程中，执行以下SQL来获取所有的类目
        sql = """
            SELECT
            a.id as category_id,
            concat(b.`category`, '_', a.`category`) category_path,
            a.type
            FROM
            `category` a
            INNER JOIN `category` b ON `a`.`parent_id` = b.`id`
            WHERE
            a.outdated = 0
            and a.parent_id is not null
            having
            category_path not like '%测试%';
        """
        result: SQLQueryResult = execute_business_query(sql)
        if result.success:
            for item in result.data:
                category_cache[item[0]] = item[1]
                category_type_cache[item[0]] = item[2]
        else:
            logger.error(f"获取类目失败: {result.error}")
    except Exception as e:
        logger.error(f"初始化类目缓存失败: {e}")
        # 设置默认值，避免后续错误
        category_cache["0"] = "其他"
        category_type_cache["0"] = "其他"


def get_md5_encoded_string(phone_number, date, word):
    input_string = f"{phone_number}{date}{word}"
    input_bytes = input_string.encode("utf-8")
    md5_hash = hashlib.md5(input_bytes)
    md5_hex = md5_hash.hexdigest()
    return md5_hex


def _get_category_type_description(category_type: int) -> str:
    """
    根据分类类型值获取描述

    Args:
        category_type: 分类类型值

    Returns:
        str: 分类类型描述
    """
    if category_type == 2:
        return "乳制品"
    elif category_type == 3:
        return "非乳制品"
    elif category_type == 4:
        return "水果"
    else:
        return "其他"


def _get_sub_type_description(sub_type: int) -> str:
    """
    根据子类型值获取描述

    Args:
        sub_type: 子类型值

    Returns:
        str: 子类型描述
    """
    if sub_type == 5:
        return f"{sub_type}:顺鹿达商品"
    elif sub_type == 3:
        return f"{sub_type}:鲜沐自营商品"
    elif sub_type in (1, 2):
        return f"{sub_type}:代销商品"
    else:
        return f"{sub_type}:代仓商品"


def search_products_from_database(product_names: List[str]) -> List[Dict[str, Any]]:
    """
    直接从数据库搜索商品信息

    Args:
        product_names: 商品名称列表

    Returns:
        List[Dict[str, Any]]: 匹配的商品信息列表
    """
    if not product_names:
        return []

    # 构建LIKE条件，支持多个商品名称
    like_conditions = []
    for name in product_names:
        # 转义单引号，防止SQL注入
        escaped_name = name.replace("'", "''")
        like_conditions.append(f"p.`pd_name` LIKE '%{escaped_name}%'")

    where_clause = " OR ".join(like_conditions)

    sql = f"""
    SELECT
      p.`pd_name`,
      i.`sku`,
      i.weight as `规格`,
      i.sub_type,
      c.category,
      c.type as category_type,
      ppv.products_property_value as brand_name,
      sum(st.online_quantity) as online_quantity
    FROM
      products p
      LEFT JOIN `inventory` i ON p.`pd_id` = i.`pd_id`
      JOIN `category` c ON p.`category_id` = c.`id`
      LEFT JOIN area_store st on i.`sku` = st.`sku`
      LEFT JOIN `products_property_value` ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
    WHERE
      ({where_clause})
      and i.sub_type != 4
      and p.outdated = 0
      and i.outdated = 0
      group by 1,2,3,4,5,6,7;
    """

    try:
        logger.info(f"从数据库搜索商品: {product_names}")
        result: SQLQueryResult = execute_business_query(sql)

        if not result.success:
            logger.error(f"数据库搜索商品失败: {result.error}")
            return []

        products = []
        for row in result.data:
            # 映射数据库字段到返回格式
            pd_name, sku, weight, sub_type, category, category_type, brand_name, online_quantity = row

            # 处理子类型和分类类型描述
            sub_type_int = int(sub_type) if sub_type else 1
            sub_type_desc = _get_sub_type_description(sub_type_int)

            category_type_int = int(category_type) if category_type else 1
            category_description = _get_category_type_description(category_type_int)

            product_info = {
                "pd_name": pd_name or "",
                "weight": weight or "",
                "sku": sku or "",
                "category_type_description": category_description,
                "category_id": "",  # 数据库查询中没有category_id
                "category_path": category or "其他",
                "brand": brand_name or "",
                "sub_type": sub_type_desc,
                "online_quantity": int(online_quantity) if online_quantity else 0,
            }
            products.append(product_info)

        logger.info(f"数据库搜索到 {len(products)} 个商品")
        return products

    except Exception as e:
        logger.error(f"数据库搜索商品异常: {e}")
        return []


def get_token_for_phone(phone_number: str = "18618107293") -> str:
    today = datetime.now().strftime("%Y%m%d")

    cache_key = f"{phone_number}{today}"
    if cache_key in token_cache:
        logging.info(f"token_cache.get(cache_key):{token_cache.get(cache_key)}")
        return token_cache.get(cache_key)

    word = "login"
    md5_encoded_string = get_md5_encoded_string(phone_number, today, word)
    url = f"https://h5.summerfarm.net/openid?phone={phone_number}&sign={md5_encoded_string}"
    token = requests.get(url=url, timeout=2000, proxies={}).json()
    try:
        token_cache[cache_key] = token["data"]["token"]
        return token_cache.get(cache_key)
    except Exception as e:
        logging.error(f"获取token失败:{e}")
        raise e


def search_product_by_name(
    product_name: Union[str, List[str]],
    is_shunluda: bool = False,
) -> List[Dict[str, Any]]:
    """
    搜索商品，返回匹配的商品信息列表，支持搜索顺鹿达商品。
    优先从数据库直接搜索，如果没有结果则调用API接口搜索。
    支持单个商品名称或多个商品名称同时搜索。

    【非常重要】如果搜索结果中有2个以上SKU，请你一定要和用户确认，以`{pd_name}, {sku}, {weight}`的形式确认(比如: 安佳淡奶油, N001S01R005, 1L*12盒)是哪一个具体SKU(规格)，避免错误。
    【非常重要】当使用本工具查询到多个结果时，禁止让用户进行确认哪个商品，思考下，过滤结果来继续下一步；

    Args:
        product_name: 搜索关键词，可以是单个商品名称字符串（如'安佳淡奶油'）或商品名称列表（如['安佳淡奶油','香水柠檬']）。
        is_shunluda: 是否是顺鹿达商品。

    Returns:
        List[Dict[str, Any]]: 匹配的商品信息列表，每个商品包含以下字段:
            - pd_name (str): 商品名称。例如: '安佳淡奶油'
            - weight (str): 商品规格。例如: '1L*12盒'
            - sku (str): 商品SKU编码。例如: 'N001S01R005'
            - category_type_description (str): 商品分类类型描述 (例如 '其他', '乳制品', '非乳制品', '水果')。例如: '乳制品'
            - brand (str): 商品品牌（如果有的话）。例如: '安佳'
            - sub_type (str): 商品子类型原始值以及描述 (例如 1: 代销, 2: 代销, 3: 鲜沐自营, 4: 代仓, 5: 顺鹿达)。
            - online_quantity (int): 在线库存数量（仅数据库搜索时提供）。
    """

    # 标准化输入：将单个字符串转换为列表
    if isinstance(product_name, str):
        product_names = [product_name]
    else:
        product_names = product_name

    if not product_names:
        logger.warning("商品名称列表为空")
        return []

    logger.info(f"搜索商品: {product_names}, 是否顺鹿达: {is_shunluda}")

    # 第一步：尝试从数据库直接搜索
    db_results = search_products_from_database(product_names)
    if db_results:
        logger.info(f"数据库搜索成功，找到 {len(db_results)} 个商品")
        return db_results

    logger.info("数据库未找到匹配商品，尝试API搜索")

    # 第二步：如果数据库没有结果，使用API搜索
    # 初始化类目缓存（API搜索需要）
    init_category_cache()

    all_products = []

    # 对每个商品名称分别调用API搜索
    for single_product_name in product_names:
        api_results = _search_single_product_from_api(single_product_name, is_shunluda)
        all_products.extend(api_results)

    return all_products


def _search_single_product_from_api(product_name: str, is_shunluda: bool = False) -> List[Dict[str, Any]]:
    """
    从API搜索单个商品（内部函数）

    Args:
        product_name: 单个商品名称
        is_shunluda: 是否是顺鹿达商品

    Returns:
        List[Dict[str, Any]]: 匹配的商品信息列表
    """
    url = "https://h5.summerfarm.net/search/product/1/20"
    headers = {"token": get_token_for_phone()}

    params = {
        "pdName": product_name,
    }

    if is_shunluda:
        params["areaNo"] = 44240

    logger.info(f"API搜索商品: {params}")

    try:
        # 发送 GET 请求
        response = requests.get(url, headers=headers, params=params)
        # 检查响应状态码是否成功
        response.raise_for_status()

        # 解析响应为 JSON
        logger.info(f"API搜索商品响应: {response.text[0:100]}...")
        result = response.json()
        products = []

        # 检查响应数据结构，确保存在商品列表
        if "data" in result and "list" in result["data"]:
            # 遍历商品列表
            for item in result["data"]["list"]:
                # 获取分类类型值，默认为1 (其他)
                cate_type_value = item.get("cateType", 1)
                category_id = f'{item.get("categoryId", 0)}'
                category_description = _get_category_type_description(cate_type_value)

                sub_type = item.get("subType", 1)
                sub_type_desc = _get_sub_type_description(sub_type)

                # 提取基础商品信息
                product_info = {
                    "pd_name": item.get("pdName", ""),
                    "weight": item.get("weight", ""),
                    "sku": item.get("sku", ""),
                    "category_type_description": category_description,  # 使用映射后的描述
                    "category_id": category_id,
                    "category_path": category_cache.get(category_id, "其他"),
                    "brand": "",  # 初始化品牌字段
                    "sub_type": sub_type_desc,  # 使用映射后的子类型
                }

                # 查找品牌信息
                if "keyValueList" in item and isinstance(item["keyValueList"], list):
                    for kv_item in item["keyValueList"]:
                        # 如果键值对的名称是“品牌”，则提取其值
                        if kv_item.get("name") == "品牌":
                            product_info["brand"] = kv_item.get(
                                "productsPropertyValue", ""
                            )
                            break  # 找到品牌后即可停止查找

                products.append(product_info)

        logger.info(f"API搜索到 {len(products)} 个商品")
        return products
    except Exception as e:
        # 记录错误日志
        logger.exception(f"API搜索商品失败: {str(e)}")
        # 发生异常时返回空列表
        return []


tool_manager.register_as_function_tool(search_product_by_name)