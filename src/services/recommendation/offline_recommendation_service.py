"""
离线推荐任务服务。
负责定时为最近30天登录用户生成推荐问题，减少实时AI调用延迟。
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

import mysql.connector
from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.db.connection import get_db_connection, Database
from src.utils.logger import logger


class OfflineRecommendationService:
    """离线推荐任务服务"""
    
    def __init__(self):
        """
        初始化离线推荐服务
        """
        self.recommendations_per_user = 10
        self.expire_hours = 48  # 推荐48小时后过期
        
    def run_daily_recommendation_task(self):
        """
        每日运行离线推荐任务的主方法
        
        1. 获取最近30天登录用户
        2. 为每个用户生成推荐问题
        3. 存储到离线推荐表
        """
        logger.info("开始执行每日离线推荐任务")
        
        try:
            # 获取最近30天登录的用户
            active_users = self._get_active_users(days=30)
            logger.info(f"找到 {len(active_users)} 个最近30天有聊天记录的用户")
            
            if not active_users:
                logger.info("没有找到需要生成推荐的用户")
                return
            
            # 为每个用户生成推荐
            success_count = 0
            failed_count = 0
            
            for user_info in active_users:
                try:
                    self._generate_recommendations_for_user(user_info)
                    success_count += 1
                    logger.info(f"为用户 {user_info['email']} 生成推荐完成")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"为用户 {user_info['email']} 生成推荐失败: {e}")
            
            logger.info(f"离线推荐任务完成: 成功 {success_count} 个用户, 失败 {failed_count} 个用户")
            
        except Exception as e:
            logger.error(f"离线推荐任务执行失败: {e}", exc_info=True)
            raise
    
    def _get_active_users(self, days: int = 30) -> List[Dict[str, str]]:
        """
        获取最近N天有聊天记录的用户

        Args:
            days: 查找最近聊天记录的天数

        Returns:
            用户邮箱和open_id列表，基于chat_history识别活跃用户
        """
        query = """
        SELECT DISTINCT u.email, u.open_id, u.name
        FROM user u
        INNER JOIN chat_history ch ON u.email = ch.email
        WHERE ch.timestamp >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL %s DAY)) * 1000
        """

        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, (days,))
            users = cursor.fetchall()

            # 去除邮箱缺失的用户
            valid_users = [
                {
                    'email': user['email'],
                    'open_id': user['open_id'],
                    'name': user['name']
                }
                for user in users if user.get('email')
            ]

            logger.info(f"获取到 {len(valid_users)} 个最近 {days} 天活跃的用户")
            return valid_users

        except Exception as e:
            logger.error(f"获取活跃用户失败: {e}")
            raise
        finally:
            # 确保资源正确释放，防止连接池泄漏
            if cursor:
                try:
                    cursor.close()
                except Exception as cursor_error:
                    logger.error(f"关闭游标失败: {cursor_error}")
            if conn and conn.is_connected():
                try:
                    conn.close()
                except Exception as conn_error:
                    logger.error(f"关闭连接失败: {conn_error}")
    
    def _generate_recommendations_for_user(self, user_info: Dict[str, str]):
        """
        为单个用户生成推荐问题并存储
        
        Args:
            user_info: 用户信息，包含 email, open_id, name
        """
        user_email = user_info['email']
        user_open_id = user_info['open_id']
        
        # 检查是否有未过期的推荐
        if self._has_valid_recommendations(user_email):
            logger.info(f"用户 {user_email} 已有有效的离线推荐，跳过生成")
            return
        
        # 标记为正在生成
        self._update_generation_status(user_email, user_open_id, status=1)  # 生成中
        
        try:
            # 获取用户历史消息
            current_user_messages = get_user_latest_queries(
                user_email=user_email, 
                limit=15
            )
            
            # 获取其他用户的消息
            other_users_messages = get_other_users_latest_queries(
                current_user_email=user_email,
                limit=25
            )
            
            # 没有数据时给出默认推荐
            if not current_user_messages and not other_users_messages:
                default_recommendations = self._get_default_recommendations()
                self._store_recommendations(
                    user_email, user_open_id, default_recommendations
                )
                return
            
            # 创建推荐机器人并生成推荐
            bot = UserQueryRecommendationBot(
                {"email": user_email}, 
                count=self.recommendations_per_user,
                word_limit=50
            )
            
            # 运行异步方法（同步执行）
            import asyncio
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                recommendations = loop.run_until_complete(
                    bot.get_recommendations(
                        current_user_messages=current_user_messages,
                        other_users_messages=other_users_messages
                    )
                )
            finally:
                loop.close()
            
            if not recommendations:
                # 如果生成失败，使用默认推荐
                logger.warning(f"为用户 {user_email} 生成推荐为空，使用默认推荐")
                recommendations = self._get_default_recommendations()
            
            # 存储推荐结果
            self._store_recommendations(
                user_email, user_open_id, recommendations
            )
            
            # 更新状态为完成
            self._update_generation_status(user_email, user_open_id, status=2)  # 完成
            
        except Exception as e:
            # 更新状态为失败
            self._update_generation_status(
                user_email, user_open_id, 
                status=3,  # 失败
                error_message=str(e)
            )
            raise
    
    def _has_valid_recommendations(self, user_email: str) -> bool:
        """检查用户是否有未过期的推荐"""
        query = """
        SELECT COUNT(*) as cnt
        FROM user_recommendation_offline
        WHERE user_email = %s
        AND expires_at > NOW()
        AND generation_status = 2
        """
        
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            cursor.execute(query, (user_email,))
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            return result[0] > 0
        except Exception as e:
            logger.error(f"检查有效推荐失败: {e}")
            return False
    
    def _store_recommendations(self, user_email: str, user_open_id: str, recommendations: List[str]):
        """存储推荐结果到数据库"""
        now = datetime.now()
        expires_at = now + timedelta(hours=self.expire_hours)

        query = """
        INSERT INTO user_recommendation_offline
        (user_email, user_open_id, recommendations, generated_at, expires_at, generation_status)
        VALUES (%s, %s, %s, %s, %s, 2)
        ON DUPLICATE KEY UPDATE
        recommendations = VALUES(recommendations),
        generated_at = VALUES(generated_at),
        expires_at = VALUES(expires_at),
        generation_status = VALUES(generation_status),
        error_message = NULL
        """

        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            cursor.execute(
                query,
                (
                    user_email,
                    user_open_id,
                    json.dumps(recommendations),
                    now,
                    expires_at
                )
            )
            conn.commit()
            logger.info(f"成功存储用户 {user_email} 的推荐结果，共 {len(recommendations)} 条")

        except Exception as e:
            logger.error(f"存储推荐失败: {e}")
            # 如果连接存在且事务未提交，回滚事务
            if conn and conn.is_connected():
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.error(f"回滚事务失败: {rollback_error}")
            raise
        finally:
            # 确保资源正确释放，防止连接池泄漏
            if cursor:
                try:
                    cursor.close()
                except Exception as cursor_error:
                    logger.error(f"关闭游标失败: {cursor_error}")
            if conn and conn.is_connected():
                try:
                    conn.close()
                except Exception as conn_error:
                    logger.error(f"关闭连接失败: {conn_error}")
    
    def _update_generation_status(self, user_email: str, user_open_id: str,
                                  status: int, error_message: str = None):
        """更新生成状态"""
        # 修复：为INSERT语句提供recommendations字段的默认值，避免NOT NULL约束错误
        if error_message:
            query = """
            INSERT INTO user_recommendation_offline
            (user_email, user_open_id, recommendations, generated_at, expires_at, generation_status, error_message)
            VALUES (%s, %s, '[]', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), %s, %s)
            ON DUPLICATE KEY UPDATE
            generation_status = VALUES(generation_status),
            error_message = VALUES(error_message),
            updated_at = NOW()
            """
            params = (user_email, user_open_id, status, error_message)
        else:
            query = """
            INSERT INTO user_recommendation_offline
            (user_email, user_open_id, recommendations, generated_at, expires_at, generation_status)
            VALUES (%s, %s, '[]', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), %s)
            ON DUPLICATE KEY UPDATE
            generation_status = VALUES(generation_status),
            error_message = NULL,
            updated_at = NOW()
            """
            params = (user_email, user_open_id, status)

        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
        except Exception as e:
            logger.error(f"更新生成状态失败: {e}")
            # 如果连接存在且事务未提交，回滚事务
            if conn and conn.is_connected():
                try:
                    conn.rollback()
                except Exception as rollback_error:
                    logger.error(f"回滚事务失败: {rollback_error}")
        finally:
            # 确保资源正确释放，防止连接池泄漏
            if cursor:
                try:
                    cursor.close()
                except Exception as cursor_error:
                    logger.error(f"关闭游标失败: {cursor_error}")
            if conn and conn.is_connected():
                try:
                    conn.close()
                except Exception as conn_error:
                    logger.error(f"关闭连接失败: {conn_error}")
    
    def _get_default_recommendations(self) -> List[str]:
        """获取默认推荐问题（当无法从AI获取时使用）"""
        return [
            "近7天华东仓热销商品库存情况如何？",
            "我的销售团队中谁的本月新客户开发最多？",
            "最近一个月安佳全脂牛奶的销售趋势是什么？",
            "杭州门店的订单履约率最近有什么变化？",
            "仓储物流团队上周的发货效率如何？",
            "哪些商品在华东地区表现最好？",
            "近两周新增门店的合作意向分析",
            "我的客户在哪些品类上复购率最高？",
            "最近30天的客户流失情况如何？",
            "如何评估销售团队的推广效果？"
        ]
    
    def get_offline_recommendations(self, user_email: str, count: int = 6) -> List[str]:
        """
        获取用户的离线推荐问题

        Args:
            user_email: 用户邮箱
            count: 返回推荐数量，默认6个

        Returns:
            推荐问题列表
        """
        query = """
        SELECT recommendations, expires_at
        FROM user_recommendation_offline
        WHERE user_email = %s
        AND expires_at > NOW()
        AND generation_status = 2
        """

        conn = None
        cursor = None
        try:
            conn = get_db_connection(Database.CHATBI)
            cursor = conn.cursor()
            cursor.execute(query, (user_email,))
            result = cursor.fetchone()

            if result:
                try:
                    recommendations = json.loads(result[0])
                    logger.debug(f"为用户 {user_email} 获取到 {len(recommendations)} 条离线推荐，返回前 {count} 条")
                    return recommendations[:count]
                except json.JSONDecodeError:
                    logger.error(f"解析推荐JSON失败: {user_email}")
                    return []
            else:
                logger.info(f"用户 {user_email} 没有有效的离线推荐")
                return []

        except Exception as e:
            logger.error(f"获取离线推荐失败: {e}")
            return []
        finally:
            # 确保资源正确释放，防止连接池泄漏
            if cursor:
                try:
                    cursor.close()
                except Exception as cursor_error:
                    logger.error(f"关闭游标失败: {cursor_error}")
            if conn and conn.is_connected():
                try:
                    conn.close()
                except Exception as conn_error:
                    logger.error(f"关闭连接失败: {conn_error}")