"""
飞书查询处理器模块
负责协调整个查询处理流程
"""

import asyncio
import queue

from src.utils.logger import logger
from src.utils.conversation_context import conversation_context
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    send_updates_to_card,
    send_finished_message_to_card,
    get_message_content,
    MessageContent,
)
from src.services.feishu.config import FeishuConfig
from src.services.agent.base_query_processor import BaseQueryProcessor, QueryRequest, THREAD_POOL
from src.services.feishu.token_service import TokenService
from .conversation_service import ConversationService
from .card_service import CardService



class FeishuQueryProcessor(BaseQueryProcessor):
    """飞书查询处理器 - 继承BaseQueryProcessor复用核心逻辑"""

    def __init__(self):
        super().__init__(THREAD_POOL)

    async def process_stream_events(self, result, message_queue: queue.Queue, streaming_message_id: int = None) -> tuple:
        """飞书流事件处理逻辑 - 复用基类的统一处理"""
        # 直接使用基类的统一流事件处理逻辑
        return await super().process_stream_events(result, message_queue, streaming_message_id)

    def handle_message_output(self, message: dict) -> str:
        """实现飞书特有的消息输出格式"""
        return message.get("content", "")

    @staticmethod
    async def handle_agent_query(
        message_id: str,
        user_query: str,
        user_info_dict: dict,
        root_id: str = None,
        parent_id: str = None,
        image_url: str = None,
        chat_id: str = None,
    ):
        """处理用户查询并将更新流式传输到飞书卡片"""
        processor = FeishuQueryProcessor()

        # 飞书特有的前置处理
        sequence = 0
        card_id = None
        element_id = None

        try:
            user_name = user_info_dict.get("name", "unknown_user")
            user_id = user_info_dict.get("open_id", "unknown_user")
            user_email = user_info_dict.get("email", "unknown")
            user_id_hash_code = user_id[-10:]

            # 使用root_id作为conversation_id，如果没有则使用message_id，并追加用户ID的哈希值作为后缀
            conversation_id = root_id if root_id else message_id
            conversation_id = f"{conversation_id}_{user_id_hash_code}"

            # 设置conversation_id上下文，确保整个处理链路中的日志都包含conversation_id
            conversation_context.set_conversation_id(conversation_id)

            logger.info(f"开始处理用户 {user_name} 的查询: {user_query}")

            # 1. 验证对话所有权
            is_owner, is_exists = (
                await ConversationService.validate_conversation_ownership(
                    user_name, user_email, message_id, conversation_id
                )
            )
            if not is_owner and is_exists:
                logger.warning(f"对话已经存在，且不属于当前用户:{conversation_id}, {user_name} 跳过处理")
                return

            parent_conent = None
            if not is_exists and not is_owner:
                logger.warning(f"对话ID:{conversation_id}不属于当前用户:{user_name}, 将为他创建新的会话:{conversation_id}")
                if parent_id:
                    logger.info(f"尝试获取父消息 {parent_id} 的内容")
                    parent_conent: MessageContent = get_message_content(parent_id)
                    if parent_conent and parent_conent.text:
                        logger.info(
                            f"使用父消息 {parent_id} 的内容作为上下文: {parent_conent.text}"
                        )
                        user_query = f"{parent_conent.text}, {user_query}"

            # 2. 尽早创建初始卡片，让用户快速收到响应
            card_id, element_id = await CardService.create_initial_card_early(
                message_id, user_query, conversation_id, user_id
            )
            if not card_id:
                reply_simple_text_message(message_id, f"卡片创建失败，无法处理您的查询:\n{user_query}\n请稍后再试")
                return

            # 2.1 检查用户授权状态，如果没有有效token则发送授权提醒
            from src.services.feishu.user_auth_service import UserAuthService
            if UserAuthService.should_send_auth_reminder(user_id):
                if chat_id:
                    try:
                        auth_sent = UserAuthService.send_auth_reminder(chat_id)
                        if auth_sent:
                            logger.info(f"已向用户 ({user_id}) 发送授权提醒，消息ID: {message_id}, chat_id: {chat_id}")
                        else:
                            logger.warning(f"向用户 ({user_id}) 发送授权提醒失败，消息ID: {message_id}, chat_id: {chat_id}")
                    except Exception as e:
                        logger.error(f"发送授权提醒时出错: {e}", exc_info=True)
                else:
                    logger.warning(f"chat_id为空，无法向用户 ({user_id}) 发送授权提醒，消息ID: {message_id}")

            # 3. 保存用户消息到历史记录
            save_success = await ConversationService.save_user_message_to_history(
                user_name, user_email, conversation_id, user_query
            )

            if not save_success:
                logger.error(f"用户消息保存失败，但继续处理查询 - user={user_name}, email={user_email}, conversation_id={conversation_id}")
                reply_simple_text_message(message_id, f"注意：您的消息：{user_query} 可能未正确保存到历史记录中，但我们会继续处理您的查询。")
            else:
                logger.debug(f"用户消息保存成功 - conversation_id={conversation_id}")

            # 准备图片列表
            images = [image_url] if image_url else ([parent_conent.image_url] if parent_conent and parent_conent.image_url else None)
            if images and images[0] is None:
                images = None

            # 获取用户的access_token（用于飞书文档搜索等功能）
            user_open_id = user_info_dict.get("open_id")
            access_token = None
            if user_open_id:
                access_token = TokenService.get_user_access_token(user_open_id)
                if access_token:
                    logger.info(f"成功获取用户access_token: open_id={user_open_id}, token={access_token[:20]}...")
                else:
                    logger.warning(f"无法获取用户access_token: open_id={user_open_id}，飞书文档搜索功能可能不可用")
            else:
                logger.warning("用户信息中缺少open_id，无法获取access_token")

            # 创建查询请求对象
            request = QueryRequest(
                user_query=user_query,
                user_info=user_info_dict,
                conversation_id=conversation_id,
                images=images,
                access_token=access_token
            )

            # 使用基类的流式处理功能，但集成飞书卡片更新
            await processor._process_feishu_query(request, card_id)

        except Exception as e:
            await FeishuQueryProcessor._handle_error(
                e, message_id, card_id, element_id, sequence
            )
        finally:
            # 确保在请求结束时清理conversation_id上下文
            conversation_context.clear_conversation_id()

    async def _process_feishu_query(self, request: QueryRequest, card_id: str):
        """处理飞书查询的核心逻辑，集成卡片更新"""
        # 创建消息队列
        message_queue = queue.Queue()

        # 创建异步工作函数
        async_worker = self.create_async_worker(request, message_queue)

        # 在线程池中执行异步工作
        self.thread_pool.submit(async_worker)

        # 处理流式响应并更新卡片
        await self._handle_feishu_stream_response(
            request, message_queue, card_id
        )

    async def _handle_feishu_stream_response(self, request: QueryRequest,
                                           message_queue: queue.Queue,
                                           card_id: str):
        """处理飞书流式响应，更新卡片"""
        full_response = ""
        sequence = 1

        # 卡片更新控制变量
        accumulated_content = ""  # 累积的新内容
        message_steps = FeishuConfig.get_message_steps()  # 获取配置的步长（默认15字符）

        try:
            while True:
                try:
                    message = message_queue.get(timeout=600)
                except queue.Empty:
                    logger.warning("消息队列获取超时")
                    break

                if message is None:
                    # 处理结束，发送最后的累积内容
                    if accumulated_content:
                        sequence = await CardService.send_thinking_and_reply_updates(
                            card_id, full_response, sequence
                        )

                    # 完成卡片（流式保存已在基类中完成）
                    await CardService.finish_card(card_id, sequence, request.conversation_id)
                    logger.debug("飞书流式处理完成")
                    break

                if isinstance(message, dict):
                    msg_type = message.get("type")

                    # 累积内容并更新卡片
                    if msg_type == "data":
                        content = message.get("content", "")
                        full_response += content
                        accumulated_content += content

                        # 检查是否需要发送更新（基于增量内容长度）
                        if len(accumulated_content) >= message_steps:
                            sequence = await CardService.send_thinking_and_reply_updates(
                                card_id, full_response, sequence
                            )
                            accumulated_content = ""  # 重置累积内容

        except (GeneratorExit, ConnectionResetError, BrokenPipeError) as e:
            logger.warning(f"飞书客户端断开连接: {e}")
            # 处理客户端断开，提交后台任务
            self.handle_client_disconnect(request)
        except Exception as e:
            logger.exception(f"处理飞书流式响应时出错: {e}")
            # 发送错误到卡片
            await self._send_error_to_card(card_id, str(e), sequence)



    async def _send_error_to_card(self, card_id: str, error_msg: str, sequence: int):
        """发送错误消息到卡片"""
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error_msg}"
            await asyncio.to_thread(
                send_updates_to_card,
                card_id,
                error_message,
                None,
                sequence=sequence + 1,
            )
        except Exception as e:
            logger.error(f"发送错误消息到卡片时出错: {e}")







    @staticmethod
    async def _handle_error(
        error, message_id, card_id=None, element_id=None, sequence=0
    ):
        """处理查询过程中的错误

        Args:
            error: 错误对象
            message_id: 消息ID
            card_id: 卡片ID
            element_id: 元素ID
            sequence: 序列号
        """
        logger.error(f"处理Agent查询时出错: {error}", exc_info=True)

        # 尝试发送错误消息到卡片，如果可能的话，否则回复文本
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error}"
            if card_id:
                error_sequence = sequence + 1
                await asyncio.to_thread(
                    send_updates_to_card,
                    card_id,
                    error_message,
                    element_id,
                    sequence=error_sequence,
                )
                finish_sequence = error_sequence + 1
                await asyncio.to_thread(
                    send_finished_message_to_card,
                    card_id,
                    sequence=finish_sequence,
                    chat_id="invalid_chat_id",
                )
            else:
                reply_simple_text_message(message_id, error_message)
        except Exception as send_error:
            logger.error(f"发送错误消息到飞书时再次出错: {send_error}")
            reply_simple_text_message(
                message_id, "处理您的请求时发生内部错误，并且无法更新卡片状态。"
            )


# 保持向后兼容性
QueryProcessor = FeishuQueryProcessor
