"""
飞书用户服务模块
负责处理用户信息获取、验证和管理
"""
import lark_oapi as lark
from lark_oapi.api.contact.v3 import *
from lark_oapi.api.contact.v3.model import *

from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import (
    upsert_user_info,
    get_user_admin_id
)
from src.services.feishu.message_apis import lark_client
from src.models.user_info_class import UserInfo
from utils.user_utils import get_api_token
from .token_service import TokenService
from src.services.auth.user_session_service import user_session_service


class UserService:
    """飞书用户服务类"""
    
    @staticmethod
    def get_valid_user_email(user_info: dict = {}) -> str:
        """获取有效的用户邮箱
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            str: 有效的邮箱地址
        """
        email = user_info.get("email")
        if email and "@" in email:
            return email
        else:
            return user_info.get("enterprise_email", "unknown")
    
    @staticmethod
    def get_user_info(user_id: str) -> str | None:
        """使用tenant token获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            str | None: 用户信息的JSON字符串，失败时返回None
        """
        request: GetUserRequest = (
            GetUserRequest.builder()
            .user_id_type("open_id")
            .user_id(user_id)
            .department_id_type("open_department_id")
            .build()
        )

        response: GetUserResponse = lark_client.contact.v3.user.get(request)

        if not response.success():
            lark.logger.error(
                f"获取用户失败, user_id:{user_id}, reponse: {response.raw.content}"
            )
            return None

        return lark.JSON.marshal(response.data.user)
    
    @staticmethod
    def process_user_info(user_info_dict: dict) -> dict:
        """处理和完善用户信息

        Args:
            user_info_dict: 原始用户信息字典

        Returns:
            dict: 处理后的用户信息字典
        """
        # 修复邮箱字段：优先使用email，如果为空则使用enterprise_email
        email = user_info_dict.get("email")
        enterprise_email = user_info_dict.get("enterprise_email")

        if not email or "@" not in email:
            if enterprise_email and "@" in enterprise_email:
                logger.info(f"用户email为空或无效，使用enterprise_email: {enterprise_email}")
                user_info_dict["email"] = enterprise_email
            else:
                logger.warning(f"用户email和enterprise_email都无效: email={email}, enterprise_email={enterprise_email}")
                user_info_dict["email"] = "unknown"

        # 确保admin_id存在
        if "admin_id" not in user_info_dict or not user_info_dict["admin_id"]:
            name = user_info_dict.get("name")
            email = UserService.get_valid_user_email(user_info_dict)
            admin_id = get_user_admin_id(user_name=name, email=email)
            if admin_id:
                logger.info(f"成功获取admin_id: {admin_id}, username:{name}, email:{email}")
                user_info_dict["admin_id"] = admin_id
            else:
                logger.error(f"无法获取admin_id, username:{name}, email:{email}")

        # 添加API Token
        union_id = user_info_dict.get("union_id")
        if union_id and not user_info_dict.get("summerfarm_api_token"):
            api_token = get_api_token(union_id=union_id)
            logger.info(f"获取到了 api_token:{api_token}")
            user_info_dict["summerfarm_api_token"] = api_token
        
        # 尝试从用户session中获取用户的access_token
        open_id = user_info_dict.get("open_id")
        if open_id and not user_info_dict.get("user_access_token"):
            try:
                # 通过open_id查找活跃的用户session
                from src.repositories.chatbi.user_session import UserSessionRepository
                session_repo = UserSessionRepository()
                active_sessions = session_repo.find_active_sessions_by_open_id(open_id)
                
                if active_sessions:
                    # 获取最新的活跃session
                    latest_session = max(active_sessions, key=lambda s: s.last_active_at)
                    if latest_session.access_token and not latest_session.is_access_token_expired():
                        user_info_dict["user_access_token"] = latest_session.access_token
                        logger.info(f"成功从session获取用户access_token: {open_id}")
                    else:
                        logger.warning(f"用户session中的access_token已过期或不存在: {open_id}")
                else:
                    logger.warning(f"未找到用户的活跃session: {open_id}")
            except Exception as e:
                logger.error(f"获取用户access_token时出错: {e}", exc_info=True)
        
        return user_info_dict
    
    @staticmethod
    def upsert_user_info_to_db(user_info_dict: dict, open_id: str):
        """将用户信息保存到数据库

        Args:
            user_info_dict: 用户信息字典
            open_id: 用户开放ID
        """
        # Prepare user info dict with all required fields
        # 使用get_valid_user_email确保获取正确的邮箱
        valid_email = UserService.get_valid_user_email(user_info_dict)

        user_info = {
            "name": user_info_dict.get("name", ""),
            "email": valid_email,  # 使用处理后的有效邮箱
            "user_id": user_info_dict.get("user_id", ""),
            "job_title": user_info_dict.get("job_title", ""),
            "first_level_department": user_info_dict.get("first_level_department", ""),  # 添加一级部门
            "open_id": open_id,
            "avatar": user_info_dict.get("avatar", ""),
            "union_id": user_info_dict.get("union_id", ""),  # 添加union_id
        }

        logger.info(f"保存用户信息到数据库: name={user_info['name']}, email={user_info['email']}, open_id={open_id}")

        # Call the upsert function with the complete user info dict
        upsert_user_info(user_info)
    
    @staticmethod
    def create_user_info_object(user_info_dict: dict) -> UserInfo:
        """创建UserInfo对象
        
        Args:
            user_info_dict: 用户信息字典
            
        Returns:
            UserInfo: 用户信息对象
        """
        # 从user_session获取用户的access token
        open_id = user_info_dict.get("open_id")
        access_token = ""
        
        if open_id:
            user_access_token = TokenService.get_user_access_token(open_id)
            if user_access_token:
                access_token = user_access_token
                logger.info(f"成功获取用户access token: open_id={open_id}, token_length={len(access_token)}")
            else:
                logger.error(f"无法获取用户access token，可能原因：1)用户未登录或session过期 2)token刷新失败: open_id={open_id}")
                # 检查是否有活跃的session
                try:
                    sessions = user_session_service.repository.find_active_sessions_by_open_id(open_id)
                    if not sessions:
                        logger.error(f"用户没有活跃的session，需要重新登录: open_id={open_id}")
                    else:
                        session = sessions[0]
                        from datetime import datetime
                        time_until_expiry = (session.access_token_expires_at - datetime.now()).total_seconds() / 60 if session.access_token_expires_at else None
                        logger.error(f"用户有活跃session但token获取失败: session_id={session.session_id}, access_token_expires_at={session.access_token_expires_at}, time_until_expiry={time_until_expiry}分钟")
                        if time_until_expiry and time_until_expiry < 0:
                            logger.error(f"用户access token已过期，需要重新登录: open_id={open_id}")
                        elif session.refresh_token:
                            logger.error(f"用户有refresh token但刷新失败，可能refresh token已失效: open_id={open_id}")
                        else:
                            logger.error(f"用户session缺少refresh token: open_id={open_id}")
                except Exception as e:
                    logger.error(f"检查用户session时出错: {e}")
        else:
            logger.warning("用户信息中缺少open_id，无法获取access token")
        
        return UserInfo(
            user_name=user_info_dict.get("name", "unknown_user"),
            email=UserService.get_valid_user_email(user_info_dict),  # 使用get_valid_user_email获取正确邮箱
            access_token=access_token,
            union_id=user_info_dict.get("union_id", ""),
            summerfarm_api_token=user_info_dict.get("summerfarm_api_token", ""),
            open_id=user_info_dict.get("open_id", ""),
        )

    @staticmethod
    def update_user_first_level_department(open_id: str) -> bool:
        """更新用户的一级部门信息

        Args:
            open_id: 用户的open_id

        Returns:
            bool: 更新是否成功
        """
        try:
            # 获取用户的一级部门
            from src.services.feishu.department_service import DepartmentService
            first_level_department = DepartmentService.get_user_first_level_department(open_id)

            if first_level_department:
                # 先获取用户信息以获得email
                user_info_json = UserService.get_user_info(open_id)
                if not user_info_json:
                    logger.error(f"无法获取用户信息: open_id={open_id}")
                    return False

                import json
                user_info = json.loads(user_info_json)
                email = user_info.get("email")
                if not email:
                    logger.error(f"用户信息中没有email: open_id={open_id}")
                    return False

                # 更新数据库中的一级部门信息
                from src.services.auth.user_login_with_feishu import update_user_first_level_department
                success = update_user_first_level_department(email, first_level_department)

                if success:
                    logger.info(f"成功更新用户一级部门: email={email}, department={first_level_department}")
                    return True
                else:
                    logger.error(f"更新用户一级部门到数据库失败: email={email}")
                    return False
            else:
                logger.info(f"用户未分配部门，跳过一级部门更新: open_id={open_id}")
                return True  # 返回True，因为这不是错误，只是用户没有部门

        except Exception as e:
            logger.error(f"更新用户一级部门时出错: open_id={open_id}, error={e}", exc_info=True)
            return False
