agent_name: sales_kpi_analytics
model_provider: openrouter
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
need_system_prompt: true
datasource_name: logical_dw
tools:
  - name: fetch_mysql_sql_result
  - name: get_sales_manager_team_members
  - name: fetch_ddl_for_table
# 配置在哪些工具执行后停止，允许用户查看结果
stop_at_tool_names:
  - fetch_mysql_sql_result
agent_description: sales_kpi_analytics.md
agent_tables:
  - name: admin
    desc: 大客户表(admin_type=0)，记录大客户ID(admin_id)、大客户名字(name_remakes)、大客户所属的销售员ID(saler_id)等
  - name: crm_bd_org
    desc: 销售组织架构表，记录销售人员的基本信息，包括销售人员ID(bd_id)、销售人员名字(bd_name)、销售人员所属的上级主管名字(parent_name)等
  - name: follow_up_relation
    desc: 商户销售私海关系表(reassign=0表示私海客户)，记录商户ID(m_id)、商户所属的销售员ID(admin_id)、商户所属的销售员名字(admin_name)等
  - name: follow_up_record
    desc: 记录商户被拜访的记录(有时也叫打卡记录），包括商户ID(m_id)、拜访人ID(admin_id)、拜访人名字(admin_name)、商户所属的运营服务区编码(area_no)等
  - name: bd_mtd_comm_df
    desc: BD每个月的绩效汇总表，记录每个BD销售人员的月度累计业绩数据，包括BD当月累计获得的利润积分、客户数量、佣金收入、品类推广、履约业绩等关键指标，用于BD绩效考核、佣金计算和业务分析。每个BD每天一条记录，支持按时间维度追踪业绩变化趋势。主要业务场景：1)BD绩效排名和考核 2)佣金计算和分配 3)客户价值分析 4)品类推广效果评估 5)GMV和利润分析等
  - name: customer_usage_data
    desc: 客户用量数据表，记录每个客户在各个品类上的用量信息，包括实际用量和预估提升量，用于后续的用量分析和预测，主要使用客户名字或者m_id来查询
  - name: cust_mtd_performance_df
    desc: 当月BD的每个客户的绩效表现，含是否达到高价值客户门槛、客户履约GMV、自营商品毛利润、类目利润得分、SPU数、超额SPU数、超额SPU数的佣金、总佣金等信息，用于BD绩效考核、佣金计算和业务分析
agent_as_tool_description: |
  这是一个专门用于销售KPI分析的AI机器人，用来查询和分析销售人员的业绩数据。业绩中最重要的是高价值客户数量及其履约GMV，特别是自营商品的履约GMV。

  ## 和sales_order_analytics的区别

  1. sales_order_analytics机器人关注的是实时数据，而sales_kpi_analytics机器人关注的是历史数据，且是经过BI审计后的ETL处理的数据。
  2. sales_order_analytics机器人关注的是销售订单层面的分析，而sales_kpi_analytics机器人关注的是销售人员的业绩数据。

  ## 销售人员业绩定义

  已经经过BI审计完成后，归属给BD销售人员的客户下单GMV。客户下单的GMV会归属给下单那一刻当时拜访的BD，而不一定是当前的BD。这是非常大的区别。
  客户和BD的关联关系是会随着时间的推移而产生变动的。这个Agent所记录的数据都是已经发生过的数据，相当于是事实快照。

  ## 其他场景

  1. 客户的在每个类目的历史实际用量和预估提升量，比如"查询某个客户的高筋面粉的历史实际用量和预估提升量"等，用户可能会先查询客户的预估用量后，再取查询xianmudb以获取客户当前距离预估用量的差距。
  2. 用本Agent来处理此类查询再好不过了。
